# <PERSON><PERSON> Glow Hub

## Sobre o Projeto

O **<PERSON><PERSON>low Hub** é uma plataforma de tecnologia jurídica (LegalTech) desenvolvida para revolucionar a forma como escritórios de advocacia e profissionais do direito trabalham. Nossa solução oferece inteligência artificial especializada em questões legais, automatização de processos e ferramentas avançadas para gestão jurídica.

## Funcionalidades Principais

- 🤖 **IA Jurídica**: Assistente inteligente especializado em direito brasileiro
- ⚖️ **Gerador de Peças**: Criação automática de petições e documentos jurídicos
- 📄 **Análise Documental**: Processamento automatizado de contratos e documentos
- 👥 **Gestão de Clientes**: Sistema completo de CRM jurídico
- 📅 **Agenda**: Controle de prazos e compromissos processuais
- 📚 **Jurisprudência**: Pesquisa em bases de dados jurídicas

## Como executar o projeto

### Pré-requisitos

- Node.js (versão 18 ou superior)
- npm ou yarn

### Instalação

```bash
# 1. Clone o repositório
git clone <URL_DO_REPOSITORIO>

# 2. Navegue até o diretório do projeto
cd juris-glow-hub

# 3. Instale as dependências
npm install

# 4. Inicie o servidor de desenvolvimento
npm run dev
```

O projeto estará disponível em `http://localhost:8080`

## Tecnologias Utilizadas

Este projeto foi desenvolvido com as seguintes tecnologias:

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Framework**: Tailwind CSS + shadcn/ui
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions)
- **Estado**: TanStack Query (React Query)
- **Roteamento**: React Router DOM
- **Ícones**: Lucide React

## Scripts Disponíveis

```bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Build para desenvolvimento
npm run build:dev

# Lint do código
npm run lint

# Preview da build
npm run preview
```

## Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes de interface
│   └── ...
├── pages/              # Páginas da aplicação
├── hooks/              # Hooks customizados
├── integrations/       # Integrações (Supabase)
├── lib/                # Utilitários
└── ...
```

## Configuração do Ambiente

Para configurar o projeto, você precisará criar um arquivo `.env.local` com as seguintes variáveis:

```env
VITE_SUPABASE_URL=sua_url_do_supabase
VITE_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase
```

## Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.
