import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { ThemeToggle } from "@/components/theme-toggle"
import { Scale, Brain, FileText, Users, Gavel, Shield, ArrowRight, CheckCircle } from "lucide-react"
import { Link } from "react-router-dom"

export function LandingPage() {
  const features = [
    {
      icon: Brain,
      title: "IA Jurídica",
      description: "Inteligência artificial especializada em questões legais e jurisprudência.",
      color: "blue" as const
    },
    {
      icon: FileText,
      title: "Análise Documental",
      description: "Análise automatizada de contratos e documentos legais.",
      color: "purple" as const
    },
    {
      icon: Gavel,
      title: "Gerador de Peças",
      description: "Criação automática de petições e documentos jurídicos.",
      color: "green" as const
    },
    {
      icon: Users,
      title: "Gestão de Clientes",
      description: "Sistema completo para gerenciar clientes e casos.",
      color: "orange" as const
    }
  ]

  const benefits = [
    "Redução de 80% no tempo de elaboração de peças",
    "IA especializada em direito brasileiro",
    "Interface intuitiva e responsiva",
    "Segurança e conformidade LGPD",
    "Integração com sistemas existentes",
    "Suporte técnico especializado"
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Scale className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold bg-gradient-legal bg-clip-text text-transparent">
              Juris Glow Hub
            </span>
          </div>
          
          <nav className="hidden md:flex space-x-6">
            <a href="#features" className="text-muted-foreground hover:text-primary transition-colors">
              Funcionalidades
            </a>
            <a href="#benefits" className="text-muted-foreground hover:text-primary transition-colors">
              Benefícios
            </a>
            <a href="#pricing" className="text-muted-foreground hover:text-primary transition-colors">
              Preços
            </a>
          </nav>

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Link to="/auth">
              <Button variant="outline">Acessar Sistema</Button>
            </Link>
            <Button className="bg-gradient-legal hover:opacity-90">
              Teste Grátis
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              O Futuro da
              <span className="bg-gradient-legal bg-clip-text text-transparent"> Advocacia</span>
              <br />
              Chegou
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Revolucione seu escritório de advocacia com nossa plataforma de IA especializada 
              em direito. Automatize processos, gere documentos e tome decisões mais assertivas.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-legal hover:opacity-90 text-lg px-8">
                Começar Agora
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8">
                Agendar Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Funcionalidades Poderosas
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Tudo que você precisa para modernizar seu escritório de advocacia
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <div key={index} style={{ animationDelay: `${index * 0.1}s` }}>
                <GlowCard 
                  glowColor={feature.color}
                  customSize
                  className="w-full h-64 animate-fade-in"
                >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-primary/10 border border-primary/20">
                    <feature.icon className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
                </GlowCard>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Por que escolher a LegalTech?
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                    <span className="text-lg">{benefit}</span>
                  </div>
                ))}
              </div>
              <Button size="lg" className="mt-8 bg-gradient-legal hover:opacity-90">
                Saiba Mais
              </Button>
            </div>
            
            <div className="relative">
              <Card className="p-8 shadow-legal border-primary/20">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">Produtividade</span>
                    <span className="text-2xl font-bold text-primary">+300%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">Tempo Economizado</span>
                    <span className="text-2xl font-bold text-accent">80%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">Satisfação dos Clientes</span>
                    <span className="text-2xl font-bold text-primary">95%</span>
                  </div>
                  <div className="pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-5 w-5 text-primary" />
                      <span className="text-sm font-medium">Certificação LGPD</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-legal">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Pronto para revolucionar sua advocacia?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Junte-se a centenas de advogados que já transformaram seus escritórios
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-lg px-8">
              Teste Grátis por 14 Dias
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 border-white text-white hover:bg-white hover:text-primary">
              Falar com Especialista
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Scale className="h-6 w-6 text-primary" />
              <span className="text-lg font-bold">LegalTech</span>
            </div>
            <div className="text-sm text-muted-foreground">
              © 2024 LegalTech. Todos os direitos reservados.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}